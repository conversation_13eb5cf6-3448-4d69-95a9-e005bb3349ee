<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "搜索规格",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black",
    "backgroundColor": "#ffffff"
  }
}
</route>

<script lang="ts" setup>
import type { IPhotoItem, IPhotoSearchParams } from '@/api/photo'
import { searchPhotoList } from '@/api/photo'

defineOptions({
  name: 'SearchPage',
})

// 搜索关键词
const searchValue = ref('')

// 搜索结果列表
const photoSizeList = ref<IPhotoItem[]>([])

// z-paging实例引用
const pagingRef = ref()

// 热门搜索关键词
const hotSearchKeywords = ['一寸', '二寸', '小一寸']

// 监听搜索值变化
watch(searchValue, (newValue) => {
  if (newValue && newValue.trim()) {
    // 重置分页并搜索
    pagingRef.value?.reload()
  }
  else {
    // 清空搜索结果
    photoSizeList.value = []
  }
})

// 热门搜索点击
function onHotSearch(keyword: string) {
  searchValue.value = keyword
}

// 查询搜索数据
async function querySearchList(pageNo: number, pageSize: number) {
  if (!searchValue.value.trim()) {
    pagingRef.value?.complete([])
    return
  }

  try {
    const params: IPhotoSearchParams = {
      pageNum: pageNo,
      pageSize,
      type: 0, // 搜索时type固定为0
      name: searchValue.value.trim(),
    }

    const response = await searchPhotoList(params)

    // 使用 completeByTotal 方法，通过总数判断是否还有更多数据
    pagingRef.value?.completeByTotal(response.records, response.total)
  }
  catch (error) {
    console.error('搜索失败:', error)
    pagingRef.value?.complete(false)
    uni.showToast({
      title: '搜索失败，请重试',
      icon: 'none',
    })
  }
}

// 点击列表项跳转到详情页
function goToDetail(item: IPhotoItem) {
  uni.navigateTo({
    url: `/pages/spec-detail/index?data=${encodeURIComponent(JSON.stringify(item))}`,
  })
}
</script>

<template>
  <view class="search-container">
    <!-- 搜索框 - 固定在顶部 -->
    <view class="search-header">
      <wd-search
        v-model="searchValue"
        placeholder="搜索证件照名称、尺寸"
        shape="round"
        hide-cancel
      />
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 热门搜索区域 -->
      <view v-show="!searchValue" class="hot-search-section">
        <view class="hot-search-header">
          <wd-icon name="fire" color="#ec7575" />
          <text class="hot-search-title">
            热门搜索
          </text>
        </view>
        <view class="hot-search-tags">
          <view
            v-for="keyword in hotSearchKeywords"
            :key="keyword"
            class="hot-search-tag"
            @click="onHotSearch(keyword)"
          >
            <text class="tag-text">
              {{ keyword }}
            </text>
          </view>
        </view>
      </view>

      <!-- 搜索结果列表 -->
      <z-paging
        v-show="searchValue"
        ref="pagingRef"
        v-model="photoSizeList"
        :auto="false"
        :fixed="false"
        @query="querySearchList"
      >
        <!-- 自定义空状态页面 -->
        <template #empty>
          <view class="custom-empty">
            <view class="empty-icon">
              🔍
            </view>
            <view class="empty-text">
              没有找到相关尺寸
            </view>
            <view class="empty-tip">
              试试其他关键词
            </view>
          </view>
        </template>

        <!-- 搜索结果列表项 -->
        <view v-for="item in photoSizeList" :key="item.id" class="item-container">
          <view class="item-card" @click="goToDetail(item)">
            <view class="item-content">
              <view class="item-title">
                {{ item.name }}
              </view>
              <view class="item-info">
                <view class="info-row">
                  <view class="tag">
                    宽高
                  </view>
                  <text class="info-text">
                    {{ item.widthMm }}*{{ item.heightMm }}mm
                  </text>
                  <view class="tag tag-margin">
                    像素
                  </view>
                  <text class="info-text">
                    {{ item.widthPx }}*{{ item.heightPx }}px
                  </text>
                </view>
              </view>
            </view>
            <view class="item-icon">
              <image class="camera-icon" src="/static/icon/camera.png" mode="aspectFit" />
            </view>
          </view>
        </view>
      </z-paging>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.search-container {
  height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.search-header {
  background-color: #fff;
  flex-shrink: 0;
}

.content-area {
  flex: 1;
  overflow: hidden;
}

.hot-search-section {
  background-color: #fff;
  padding: 30rpx;
}

.hot-search-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.hot-search-title {
  font-weight: 700;
  font-size: 28rpx;
  color: #323232;
}

.hot-search-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.hot-search-tag {
  padding: 8rpx 20rpx;
  background: #f7f8fa;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 48rpx;

  .tag-text {
    font-size: 24rpx;
    color: #666;
    line-height: 1;
  }
}

.custom-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
  }

  .empty-tip {
    font-size: 24rpx;
    color: #999;
  }
}

.item-container {
  margin: 25rpx 30rpx;
}

.item-card {
  width: 100%;
  height: 132rpx;
  background: #ffffff;
  box-shadow: 2rpx 1rpx 4rpx 3rpx rgba(52, 51, 51, 0.06);
  border-radius: 20rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.item-content {
  flex: 1;
  .item-title {
    font-weight: 600;
    font-size: 28rpx;
    line-height: 39rpx;
    color: #323232;
    margin-bottom: 10rpx;
  }
}

.info-row {
  display: flex;
  align-items: center;
}

.tag {
  height: 30rpx;
  width: 60rpx;
  background: #e8e8ff;
  border-radius: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  font-size: 20rpx;
  color: #bdbdff;
}

.tag-margin {
  margin-left: 66rpx;
}

.info-text {
  font-size: 24rpx;
  line-height: 34rpx;
  color: #d3d3d3;
  margin-left: 16rpx;
}

.item-icon {
  .camera-icon {
    width: 44rpx;
    height: 44rpx;
  }
}
</style>
